<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title>SPCA IPG UAT Test</title>
  <!-- CryptoJS for HMAC-SHA256 -->
  <script src="https://cdnjs.cloudflare.com/ajax/libs/crypto-js/4.1.1/crypto-js.min.js"></script>
</head>
<body>
  <h1>SPCA Fiji Donation UAT Test</h1>
  <form id="donationForm" method="post" action="https://uat2.yalamanchili.in/MPI_v1/mercpg">
    <!-- Required AR fields -->
    <input type="hidden" name="nar_msgType"       value="AR">
    <input type="hidden" name="nar_merBankCode"   value="01">
    <input type="hidden" name="nar_merId"         value="****************">
    <input type="hidden" name="nar_merTxnTime"    id="nar_merTxnTime"    value="">
    <input type="hidden" name="nar_orderNo"       id="nar_orderNo"       value="">
    <input type="hidden" name="nar_txnCurrency"   value="242">
    <input type="hidden" name="nar_txnAmount"     id="nar_txnAmount"     value="0.05">
    <!-- Optional donor info -->
    <input type="hidden" name="nar_remitterEmail"  id="nar_remitterEmail"  value="">
    <input type="hidden" name="nar_remitterMobile" id="nar_remitterMobile" value="">
    <!-- Security & return -->
    <input type="hidden" name="nar_cardType"       value="EX">
    <input type="hidden" name="nar_Secure"         value="MERSECURE">
    <input type="hidden" name="nar_returnUrl"      value="https://donations.spcafiji.com/thank-you">
    <!-- Metadata -->
    <input type="hidden" name="nar_paymentDesc"    value="SPCA Fiji Donation">
    <input type="hidden" name="nar_version"        value="1.0">
    <!-- Computed HMAC -->
    <input type="hidden" name="nar_checkSum"       id="nar_checkSum"       value="">
    <button type="button" onclick="submitForm()">Donate FJD 0.05</button>
  </form>

  <script>
    // simple zero-pad
    function pad(n){ return n<10 ? '0'+n : n; }

    // build timestamps & orderNo
    function buildTxnData(){
      const d = new Date();
      const YYYY = d.getFullYear(),
            MM   = pad(d.getMonth()+1),
            DD   = pad(d.getDate()),
            hh   = pad(d.getHours()),
            mm   = pad(d.getMinutes()),
            ss   = pad(d.getSeconds());
      const merTxnTime = ''+YYYY+MM+DD+hh+mm+ss;
      return {
        merTxnTime,
        orderNo: 'ORD_'+merTxnTime
      };
    }

    function submitForm(){
      // 1) Set dynamic fields
      const data = buildTxnData();
      document.getElementById('nar_merTxnTime').value = data.merTxnTime;
      document.getElementById('nar_orderNo').value    = data.orderNo;

      // 2) Collect values in alphabetical order of field NAMES
      const vals = [
        document.querySelector('input[name="nar_cardType"]').value,
        document.querySelector('input[name="nar_merBankCode"]').value,
        document.querySelector('input[name="nar_merId"]').value,
        data.merTxnTime,
        document.querySelector('input[name="nar_msgType"]').value,
        data.orderNo,
        document.querySelector('input[name="nar_paymentDesc"]').value,
        document.getElementById('nar_remitterEmail').value || '',
        document.getElementById('nar_remitterMobile').value || '',
        document.querySelector('input[name="nar_txnAmount"]').value,
        document.querySelector('input[name="nar_txnCurrency"]').value,
        document.querySelector('input[name="nar_version"]').value,
        document.querySelector('input[name="nar_returnUrl"]').value
      ];

      // 3) Build source string
      const sourceString = '|' + vals.join('|') + '|';

      // 4) Compute HMAC-SHA256
      const hmacKey = '5bf32e5becef7416efafc259bb5f5f298ee93c45f1f74ff647ed877717f7421adae7be624f531054a1eebf1664be9704b6aecb5e15826696f34bc66a99757cf3';
      const key     = CryptoJS.enc.Hex.parse(hmacKey);
      const checksum= CryptoJS.HmacSHA256(sourceString, key).toString(CryptoJS.enc.Hex);

      // 5) Insert and submit
      document.getElementById('nar_checkSum').value = checksum;
      console.log('Source String:', sourceString);
      console.log('Computed nar_checkSum:', checksum);
      document.getElementById('donationForm').submit();
    }
  </script>
</body>
</html>
