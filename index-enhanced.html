<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>SPCA IPG UAT Test - Enhanced</title>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/crypto-js/4.1.1/crypto-js.min.js"></script>
  <style>
    body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }
    .form-group { margin: 10px 0; }
    label { display: block; margin-bottom: 5px; font-weight: bold; }
    input[type="text"], input[type="email"], input[type="number"] { 
      width: 100%; padding: 8px; border: 1px solid #ccc; border-radius: 4px; 
    }
    button { background: #007cba; color: white; padding: 12px 24px; border: none; border-radius: 4px; cursor: pointer; }
    button:hover { background: #005a87; }
    .debug { background: #f5f5f5; padding: 10px; margin: 10px 0; border-radius: 4px; font-family: monospace; }
    .error { color: red; }
    .success { color: green; }
  </style>
</head>
<body>
  <h1>SPCA Fiji Donation UAT Test (Enhanced)</h1>
  
  <div class="form-group">
    <label for="donorEmail">Donor Email (Optional):</label>
    <input type="email" id="donorEmail" placeholder="<EMAIL>">
  </div>
  
  <div class="form-group">
    <label for="donorMobile">Donor Mobile (Optional):</label>
    <input type="text" id="donorMobile" placeholder="+679xxxxxxxx">
  </div>
  
  <div class="form-group">
    <label for="amount">Donation Amount (FJD):</label>
    <input type="number" id="amount" value="0.05" min="0.01" step="0.01">
  </div>
  
  <form id="donationForm" method="post" action="https://uat2.yalamanchili.in/MPI_v1/mercpg">
    <!-- Required AR fields -->
    <input type="hidden" name="nar_msgType"       value="AR">
    <input type="hidden" name="nar_merBankCode"   value="01">
    <input type="hidden" name="nar_merId"         value="****************">
    <input type="hidden" name="nar_merTxnTime"    id="nar_merTxnTime"    value="">
    <input type="hidden" name="nar_orderNo"       id="nar_orderNo"       value="">
    <input type="hidden" name="nar_txnCurrency"   value="242">
    <input type="hidden" name="nar_txnAmount"     id="nar_txnAmount"     value="0.05">
    <!-- Optional donor info -->
    <input type="hidden" name="nar_remitterEmail"  id="nar_remitterEmail"  value="">
    <input type="hidden" name="nar_remitterMobile" id="nar_remitterMobile" value="">
    <!-- Security & return -->
    <input type="hidden" name="nar_cardType"       value="EX">
    <input type="hidden" name="nar_Secure"         value="MERSECURE">
    <input type="hidden" name="nar_returnUrl"      value="https://donations.spcafiji.com/thank-you">
    <!-- Metadata -->
    <input type="hidden" name="nar_paymentDesc"    value="SPCA Fiji Donation">
    <input type="hidden" name="nar_version"        value="1.0">
    <!-- Computed HMAC -->
    <input type="hidden" name="nar_checkSum"       id="nar_checkSum"       value="">
    
    <button type="button" onclick="submitForm()">Process Donation</button>
  </form>

  <div id="debugInfo" class="debug" style="display: none;">
    <h3>Debug Information:</h3>
    <div id="debugContent"></div>
  </div>

  <script>
    // Simple zero-pad
    function pad(n){ return n<10 ? '0'+n : n; }

    // Build timestamps & orderNo
    function buildTxnData(){
      const d = new Date();
      const YYYY = d.getFullYear(),
            MM   = pad(d.getMonth()+1),
            DD   = pad(d.getDate()),
            hh   = pad(d.getHours()),
            mm   = pad(d.getMinutes()),
            ss   = pad(d.getSeconds());
      const merTxnTime = ''+YYYY+MM+DD+hh+mm+ss;
      return {
        merTxnTime,
        orderNo: 'SPCA_'+merTxnTime
      };
    }

    function validateForm() {
      const amount = parseFloat(document.getElementById('amount').value);
      if (isNaN(amount) || amount < 0.01) {
        alert('Please enter a valid amount (minimum FJD 0.01)');
        return false;
      }
      return true;
    }

    function submitForm(){
      if (!validateForm()) return;

      try {
        // 1) Set dynamic fields
        const data = buildTxnData();
        document.getElementById('nar_merTxnTime').value = data.merTxnTime;
        document.getElementById('nar_orderNo').value = data.orderNo;
        
        // Set amount and optional fields
        document.getElementById('nar_txnAmount').value = document.getElementById('amount').value;
        document.getElementById('nar_remitterEmail').value = document.getElementById('donorEmail').value;
        document.getElementById('nar_remitterMobile').value = document.getElementById('donorMobile').value;

        // 2) Collect values in alphabetical order of field NAMES
        const vals = [
          document.querySelector('input[name="nar_cardType"]').value,
          document.querySelector('input[name="nar_merBankCode"]').value,
          document.querySelector('input[name="nar_merId"]').value,
          data.merTxnTime,
          document.querySelector('input[name="nar_msgType"]').value,
          data.orderNo,
          document.querySelector('input[name="nar_paymentDesc"]').value,
          document.getElementById('nar_remitterEmail').value || '',
          document.getElementById('nar_remitterMobile').value || '',
          document.querySelector('input[name="nar_txnAmount"]').value,
          document.querySelector('input[name="nar_txnCurrency"]').value,
          document.querySelector('input[name="nar_version"]').value,
          document.querySelector('input[name="nar_returnUrl"]').value
        ];

        // 3) Build source string
        const sourceString = '|' + vals.join('|') + '|';

        // 4) Compute HMAC-SHA256
        const hmacKey = '5bf32e5becef7416efafc259bb5f5f298ee93c45f1f74ff647ed877717f7421adae7be624f531054a1eebf1664be9704b6aecb5e15826696f34bc66a99757cf3';
        const key = CryptoJS.enc.Hex.parse(hmacKey);
        const checksum = CryptoJS.HmacSHA256(sourceString, key).toString(CryptoJS.enc.Hex);

        // 5) Insert checksum
        document.getElementById('nar_checkSum').value = checksum;

        // 6) Show debug info
        const debugContent = `
          <strong>Transaction Details:</strong><br>
          Order No: ${data.orderNo}<br>
          Transaction Time: ${data.merTxnTime}<br>
          Amount: FJD ${document.getElementById('amount').value}<br>
          <br>
          <strong>Source String:</strong><br>
          <code>${sourceString}</code><br>
          <br>
          <strong>HMAC-SHA256:</strong><br>
          <code>${checksum}</code><br>
          <br>
          <span class="success">✓ Ready to submit to BSP IPG</span>
        `;
        
        document.getElementById('debugContent').innerHTML = debugContent;
        document.getElementById('debugInfo').style.display = 'block';

        console.log('=== BSP IPG Debug Info ===');
        console.log('Source String:', sourceString);
        console.log('Computed nar_checkSum:', checksum);
        console.log('Order No:', data.orderNo);
        console.log('========================');

        // 7) Submit after a brief delay to show debug info
        setTimeout(() => {
          if (confirm('Submit payment to BSP IPG?')) {
            document.getElementById('donationForm').submit();
          }
        }, 1000);

      } catch (error) {
        console.error('Error processing form:', error);
        document.getElementById('debugContent').innerHTML = `<span class="error">Error: ${error.message}</span>`;
        document.getElementById('debugInfo').style.display = 'block';
      }
    }

    // Auto-update amount display
    document.getElementById('amount').addEventListener('input', function() {
      const amount = this.value || '0.00';
      document.querySelector('button').textContent = `Process Donation - FJD ${amount}`;
    });
  </script>
</body>
</html>
