# BSP IPG Testing Checklist

## Pre-Testing Setup
- [ ] Verify test merchant credentials are active
- [ ] Ensure return URL is accessible
- [ ] Check UAT environment availability

## Test Scenarios

### 1. Successful Payment
- [ ] Submit form with valid data
- [ ] Verify redirect to BSP payment page
- [ ] Complete payment with test card
- [ ] Check return to success URL
- [ ] Verify transaction appears in merchant portal

### 2. Hash Validation
- [ ] Check console for source string format
- [ ] Verify HMAC-SHA256 calculation
- [ ] Ensure no extra spaces or characters

### 3. Error Scenarios
- [ ] Test with invalid amount (negative/zero)
- [ ] Test with malformed data
- [ ] Test network timeout scenarios

### 4. Field Validation
- [ ] Test with missing required fields
- [ ] Test with invalid currency code
- [ ] Test with future/past timestamps

## Expected Source String Format
```
|EX|01|8001281018001014|YYYYMMDDHHMMSS|AR|ORD_YYYYMMDDHHMMSS|SPCA Fiji Donation|||0.05|242|1.0|https://donations.spcafiji.com/thank-you|
```

## Test Cards (if provided by BSP)
- Visa: ****************
- MasterCard: ****************
- Expiry: Any future date
- CVV: Any 3 digits

## Monitoring Points
- [ ] Transaction ID generation
- [ ] Response time
- [ ] Error messages
- [ ] Callback handling
